import { ThemeProvider } from '@emotion/react'

import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DateTime } from 'luxon'
import { vi } from 'vitest'

import { theme } from '@apella/component-library'
import { TimezoneProvider } from 'src/Contexts'

import { ScheduleFilterContext } from './ScheduleFilterContext'
import { ShowClosedRoomsToggle } from './ShowClosedRoomsToggle'
import { TimeRange } from './types'

const createMockContext = (overrides = {}) => ({
  sites: [],
  rooms: [],
  onChangeSurgeons: vi.fn(),
  maxTime: '',
  minTime: DateTime.now().toISO(),
  onChangeDate: vi.fn(),
  onChangeSites: vi.fn(),
  onChangeRooms: vi.fn(),
  onToggleScheduled: vi.fn(),
  showResetFiltersButton: false,
  showScheduled: true,
  onChangeDailyMetric: vi.fn(),
  resetFilters: vi.fn(),
  onToggleShowClosedRooms: vi.fn(),
  onToggleShowMetrics: vi.fn(),
  showClosedRooms: true,
  showClosedRoomsUserPreference: true,
  isViewingPastDate: false,
  showMetrics: true,
  sortKeys: [],
  onToggleSortKey: vi.fn(),
  onToggleFilters: vi.fn(),
  timeRange: TimeRange.DayBound,
  ...overrides,
})

const renderWithProviders = (
  component: React.ReactElement,
  contextOverrides = {},
  timezone = 'America/New_York'
) => {
  const mockContext = createMockContext(contextOverrides)

  return render(
    <ThemeProvider theme={theme}>
      <TimezoneProvider orgDefaultTimezone={timezone}>
        <ScheduleFilterContext.Provider value={mockContext}>
          {component}
        </ScheduleFilterContext.Provider>
      </TimezoneProvider>
    </ThemeProvider>
  )
}

describe('Closed Rooms Filter Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Present Date Behavior', () => {
    it('should allow toggling the filter when viewing today', async () => {
      const user = userEvent.setup()
      const mockToggle = vi.fn()
      const today = DateTime.now().toISO()

      renderWithProviders(<ShowClosedRoomsToggle />, {
        minTime: today,
        showClosedRoomsUserPreference: false,
        isViewingPastDate: false,
        onToggleShowClosedRooms: mockToggle,
      })

      const button = screen.getByRole('button')
      expect(button).not.toBeDisabled()

      await user.click(button)
      expect(mockToggle).toHaveBeenCalledWith(true)
    })

    it('should show correct tooltip for enabled filter on present date', async () => {
      const user = userEvent.setup()
      const today = DateTime.now().toISO()

      renderWithProviders(<ShowClosedRoomsToggle />, {
        minTime: today,
        showClosedRoomsUserPreference: true,
        isViewingPastDate: false,
      })

      const button = screen.getByRole('button')
      await user.hover(button)

      expect(screen.getByText('Hide closed rooms')).toBeInTheDocument()
    })
  })

  describe('Past Date Behavior', () => {
    it('should disable the filter when viewing past dates', () => {
      const pastDate = DateTime.now().minus({ days: 3 }).toISO()

      renderWithProviders(<ShowClosedRoomsToggle />, {
        minTime: pastDate,
        showClosedRoomsUserPreference: true,
        isViewingPastDate: true,
      })

      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
    })

    it('should show explanatory tooltip when viewing past dates', async () => {
      const user = userEvent.setup()
      const pastDate = DateTime.now().minus({ days: 3 }).toISO()

      renderWithProviders(<ShowClosedRoomsToggle />, {
        minTime: pastDate,
        showClosedRoomsUserPreference: true,
        isViewingPastDate: true,
      })

      const button = screen.getByRole('button')
      await user.hover(button)

      expect(screen.getByText('Inactive on historical data')).toBeInTheDocument()
    })

    it('should not call toggle function when disabled button is clicked', async () => {
      const user = userEvent.setup()
      const mockToggle = vi.fn()
      const pastDate = DateTime.now().minus({ days: 3 }).toISO()

      renderWithProviders(<ShowClosedRoomsToggle />, {
        minTime: pastDate,
        showClosedRoomsUserPreference: true,
        isViewingPastDate: true,
        onToggleShowClosedRooms: mockToggle,
      })

      const button = screen.getByRole('button')
      await user.click(button)

      expect(mockToggle).not.toHaveBeenCalled()
    })
  })

  describe('Filter State Management', () => {
    it('should preserve user preference when filter is disabled for past dates', () => {
      const pastDate = DateTime.now().minus({ days: 3 }).toISO()

      const { rerender } = renderWithProviders(<ShowClosedRoomsToggle />, {
        minTime: pastDate,
        showClosedRoomsUserPreference: false, // User had it disabled
        isViewingPastDate: true,
        showClosedRooms: true, // But effective value is true for past dates
      })

      const button = screen.getByRole('button')
      expect(button).toBeDisabled()

      // Simulate navigating back to today
      rerender(
        <ThemeProvider theme={theme}>
          <TimezoneProvider orgDefaultTimezone="America/New_York">
            <ScheduleFilterContext.Provider value={createMockContext({
              minTime: DateTime.now().toISO(),
              showClosedRoomsUserPreference: false, // User preference preserved
              isViewingPastDate: false,
              showClosedRooms: false, // Effective value matches user preference
            })}>
              <ShowClosedRoomsToggle />
            </ScheduleFilterContext.Provider>
          </TimezoneProvider>
        </ThemeProvider>
      )

      const buttonAfterReturn = screen.getByRole('button')
      expect(buttonAfterReturn).not.toBeDisabled()
    })
  })
})
