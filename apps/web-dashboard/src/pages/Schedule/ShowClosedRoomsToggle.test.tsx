import { ThemeProvider } from '@emotion/react'

import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { DateTime } from 'luxon'
import { vi } from 'vitest'

import { theme } from '@apella/component-library'
import { TimezoneProvider } from 'src/Contexts'

import { ScheduleFilterContext } from './ScheduleFilterContext'
import { ShowClosedRoomsToggle } from './ShowClosedRoomsToggle'
import { TimeRange } from './types'

const createMockContext = (overrides = {}) => ({
  sites: [],
  rooms: [],
  onChangeSurgeons: vi.fn(),
  maxTime: '',
  minTime: DateTime.now().toISO(),
  onChangeDate: vi.fn(),
  onChangeSites: vi.fn(),
  onChangeRooms: vi.fn(),
  onToggleScheduled: vi.fn(),
  showResetFiltersButton: false,
  showScheduled: true,
  onChangeDailyMetric: vi.fn(),
  resetFilters: vi.fn(),
  onToggleShowClosedRooms: vi.fn(),
  onToggleShowMetrics: vi.fn(),
  showClosedRooms: true,
  showClosedRoomsUserPreference: true,
  isViewingPastDate: false,
  showMetrics: true,
  sortKeys: [],
  onToggleSortKey: vi.fn(),
  onToggleFilters: vi.fn(),
  timeRange: TimeRange.DayBound,
  ...overrides,
})

const renderWithProviders = (
  component: React.ReactElement,
  contextOverrides = {},
  timezone = 'America/New_York'
) => {
  const mockContext = createMockContext(contextOverrides)

  return render(
    <ThemeProvider theme={theme}>
      <TimezoneProvider orgDefaultTimezone={timezone}>
        <ScheduleFilterContext.Provider value={mockContext}>
          {component}
        </ScheduleFilterContext.Provider>
      </TimezoneProvider>
    </ThemeProvider>
  )
}

describe('ShowClosedRoomsToggle', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render enabled button when viewing present date', () => {
    renderWithProviders(<ShowClosedRoomsToggle />, {
      showClosedRoomsUserPreference: true,
      isViewingPastDate: false,
    })

    const button = screen.getByRole('button')
    expect(button).not.toBeDisabled()
  })

  it('should render disabled button when viewing past date', () => {
    renderWithProviders(<ShowClosedRoomsToggle />, {
      showClosedRoomsUserPreference: true,
      isViewingPastDate: true,
    })

    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
  })

  it('should show correct tooltip when viewing past date', async () => {
    const user = userEvent.setup()

    renderWithProviders(<ShowClosedRoomsToggle />, {
      showClosedRoomsUserPreference: true,
      isViewingPastDate: true,
    })

    const button = screen.getByRole('button')
    await user.hover(button)

    expect(screen.getByText('Inactive on historical data')).toBeInTheDocument()
  })

  it('should show correct tooltip when filter is enabled', async () => {
    const user = userEvent.setup()

    renderWithProviders(<ShowClosedRoomsToggle />, {
      showClosedRoomsUserPreference: true,
      isViewingPastDate: false,
    })

    const button = screen.getByRole('button')
    await user.hover(button)

    expect(screen.getByText('Hide closed rooms')).toBeInTheDocument()
  })

  it('should show correct tooltip when filter is disabled', async () => {
    const user = userEvent.setup()

    renderWithProviders(<ShowClosedRoomsToggle />, {
      showClosedRoomsUserPreference: false,
      isViewingPastDate: false,
    })

    const button = screen.getByRole('button')
    await user.hover(button)

    expect(screen.getByText('Show closed rooms')).toBeInTheDocument()
  })

  it('should call onToggleShowClosedRooms with correct value when clicked', async () => {
    const user = userEvent.setup()
    const mockToggle = vi.fn()

    renderWithProviders(<ShowClosedRoomsToggle />, {
      showClosedRoomsUserPreference: false,
      isViewingPastDate: false,
      onToggleShowClosedRooms: mockToggle,
    })

    const button = screen.getByRole('button')
    await user.click(button)

    expect(mockToggle).toHaveBeenCalledWith(true)
  })

  it('should not call onToggleShowClosedRooms when disabled and clicked', async () => {
    const user = userEvent.setup()
    const mockToggle = vi.fn()

    renderWithProviders(<ShowClosedRoomsToggle />, {
      showClosedRoomsUserPreference: true,
      isViewingPastDate: true,
      onToggleShowClosedRooms: mockToggle,
    })

    const button = screen.getByRole('button')
    await user.click(button)

    expect(mockToggle).not.toHaveBeenCalled()
  })

  it('should render button with correct styling when filter is active', () => {
    renderWithProviders(<ShowClosedRoomsToggle />, {
      showClosedRoomsUserPreference: true,
      isViewingPastDate: false,
    })

    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
    // The button should be rendered and functional when active
    expect(button).not.toBeDisabled()
  })

  it('should render button with correct styling when filter is inactive', () => {
    renderWithProviders(<ShowClosedRoomsToggle />, {
      showClosedRoomsUserPreference: false,
      isViewingPastDate: false,
    })

    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
    // The button should be rendered and functional when inactive
    expect(button).not.toBeDisabled()
  })
})
