import { createContext, useContext } from 'react'

import { LocationPath } from 'src/router/types'
import { EVENTS } from 'src/utils/analyticsEvents'
import { LiveStatus } from 'src/utils/status'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import { TimeRange, ViewRoomMetric } from './types'

interface ScheduleFilterContext {
  maxTime: string
  minTime: string
  numCols?: number
  numFiltersApplied?: number
  onChangeDailyMetric: (metric?: ViewRoomMetric) => void
  onChangeDate: (date: Date) => void
  onChangeNumCols?: (numCols?: number) => void
  onChangeRooms: (roomsIds?: string[]) => void
  onChangeSites: (siteId?: string[]) => void
  onChangeSurgeons: (surgeonIds?: string[]) => void
  onChangeTimeRange?: (timeRange: TimeRange) => void
  onToggleFilters: (val: boolean) => void
  onToggleScheduled: () => void
  onToggleShowClosedRooms: (val: boolean) => void
  onToggleShowMetrics: (val: boolean) => void
  onToggleSortKey: (sortKey: LiveStatus) => void
  resetFilters: () => void
  roomIds?: string[]
  rooms: ReturnType<typeof useRoomOptions>
  selectedSurgeons?: string[]
  showClosedRooms?: boolean
  showClosedRoomsUserPreference?: boolean
  isViewingPastDate?: boolean
  showFilters?: boolean
  showMetrics?: boolean
  showResetFiltersButton?: boolean
  showScheduled: boolean
  siteIds?: string[]
  sites: ReturnType<typeof useSiteOptions>['sites']
  sortKeys: LiveStatus[]
  timeRange: TimeRange
  viewRoomsState?: ViewRoomMetric
}

export enum ScheduleFilterProviderContext {
  SCHEDULE = 'schedule',
  EDIT_SCHEDULE = 'schedule/edit',
  TURNOVERS = 'turnovers',
  STAFF_MANAGEMENT = 'staff-management',
  LIVE = 'live',
  PRE_OP = 'pre-op',
  POST_OP = 'post-op',
}

// These are the events that will be logged for each action in the Schedule Filter Context, they are intentionally
// nullable so that we can easily disable logging for certain events. They are not undefined/optional because we want
// to explicitly set them to null when we don't want to log them - as opposed to just not setting them at all.
export interface EventLog {
  onChangeDailyMetric: EVENTS | null
  onChangeDate: EVENTS | null
  onChangeLayout: EVENTS | null
  onChangeRooms: EVENTS | null
  onChangeSites: EVENTS | null
  onChangeSurgeons: EVENTS | null
  onChangeTimeRange: EVENTS | null
  onPageLoad: EVENTS | null
  onPageUnload: EVENTS | null
  onToggleFilters: EVENTS | null
  onToggleScheduled: EVENTS | null
  onToggleShowClosedRooms: EVENTS | null
  onToggleShowMetrics: EVENTS | null
  onToggleSortKey: EVENTS | null
}

export const ScheduleFilterContext = createContext<ScheduleFilterContext>({
  sites: [],
  rooms: [],
  onChangeSurgeons: () => { },
  maxTime: '',
  minTime: '',
  onChangeDate: () => { },
  onChangeSites: () => { },
  onChangeRooms: () => { },
  onToggleScheduled: () => { },
  showResetFiltersButton: false,
  showScheduled: true,
  onChangeDailyMetric: () => { },
  resetFilters: () => { },
  onToggleShowClosedRooms: () => { },
  onToggleShowMetrics: () => { },
  showClosedRooms: true,
  showClosedRoomsUserPreference: true,
  isViewingPastDate: false,
  showMetrics: true,
  sortKeys: [],
  onToggleSortKey: () => { },
  onToggleFilters: () => { },
  timeRange: TimeRange.DayBound,
})

export const useScheduleFilterContext = () => useContext(ScheduleFilterContext)

export const showScheduledRoutes: Partial<{ [key: string]: boolean }> = {
  [LocationPath.EditSchedule]: true,
}

export const eventMapper: { [key in ScheduleFilterProviderContext]: EventLog } =
  {
    [ScheduleFilterProviderContext.SCHEDULE]: {
      onChangeSites: EVENTS.SCHEDULE_PAGE_FILTER_SITES,
      onChangeRooms: EVENTS.SCHEDULE_PAGE_FILTER_ROOMS,
      onChangeDate: EVENTS.SCHEDULE_PAGE_FILTER_DATE,
      onChangeSurgeons: EVENTS.SCHEDULE_PAGE_FILTER_SURGEONS,
      onChangeTimeRange: EVENTS.SCHEDULE_PAGE_FILTER_TIME_RANGE,
      onChangeDailyMetric: EVENTS.SCHEDULE_PAGE_FILTER_DAILY_METRIC,
      onChangeLayout: EVENTS.SCHEDULE_PAGE_FILTER_LAYOUT,
      onToggleFilters: EVENTS.SCHEDULE_PAGE_TOGGLE_FILTERS,
      onToggleScheduled: EVENTS.SCHEDULE_PAGE_TOGGLE_SCHEDULED,
      onToggleShowClosedRooms: EVENTS.SCHEDULE_PAGE_TOGGLE_SHOW_CLOSED_ROOMS,
      onToggleShowMetrics: EVENTS.SCHEDULE_PAGE_TOGGLE_SHOW_METRICS,
      onToggleSortKey: EVENTS.SCHEDULE_PAGE_TOGGLE_SORT_KEY,
      onPageLoad: EVENTS.SCHEDULE_PAGE_LOAD_PAGE,
      onPageUnload: EVENTS.SCHEDULE_PAGE_EXIT,
    },
    [ScheduleFilterProviderContext.PRE_OP]: {
      onChangeSites: EVENTS.PREOP_FILTER_SITES,
      onChangeRooms: EVENTS.PREOP_FILTER_ROOMS,
      onChangeDailyMetric: EVENTS.PREOP_FILTER_DAILY_METRIC,
      onChangeDate: EVENTS.PREOP_FILTER_DATE,
      onChangeLayout: EVENTS.PREOP_FILTER_LAYOUT,
      onChangeSurgeons: EVENTS.PREOP_FILTER_SURGEONS,
      onChangeTimeRange: EVENTS.PREOP_FILTER_TIME_RANGE,
      onToggleFilters: EVENTS.PREOP_TOGGLE_FILTERS,
      onToggleScheduled: EVENTS.PREOP_TOGGLE_SCHEDULED,
      onToggleShowClosedRooms: EVENTS.PREOP_TOGGLE_SHOW_CLOSED_ROOMS,
      onToggleShowMetrics: EVENTS.PREOP_TOGGLE_SHOW_METRICS,
      onToggleSortKey: EVENTS.PREOP_TOGGLE_SORT_KEY,
      onPageLoad: EVENTS.PREOP_LOAD_PAGE,
      onPageUnload: EVENTS.PREOP_PAGE_EXIT,
    },
    [ScheduleFilterProviderContext.POST_OP]: {
      onChangeSites: EVENTS.POSTOP_FILTER_SITES,
      onChangeRooms: EVENTS.POSTOP_FILTER_ROOMS,
      onChangeDailyMetric: EVENTS.POSTOP_FILTER_DAILY_METRIC,
      onChangeDate: EVENTS.POSTOP_FILTER_DATE,
      onChangeLayout: EVENTS.POSTOP_FILTER_LAYOUT,
      onChangeSurgeons: EVENTS.POSTOP_FILTER_SURGEONS,
      onChangeTimeRange: EVENTS.POSTOP_FILTER_TIME_RANGE,
      onToggleFilters: EVENTS.POSTOP_TOGGLE_FILTERS,
      onToggleScheduled: EVENTS.POSTOP_TOGGLE_SCHEDULED,
      onToggleShowClosedRooms: EVENTS.POSTOP_TOGGLE_SHOW_CLOSED_ROOMS,
      onToggleShowMetrics: EVENTS.POSTOP_TOGGLE_SHOW_METRICS,
      onToggleSortKey: EVENTS.POSTOP_TOGGLE_SORT_KEY,
      onPageLoad: EVENTS.POSTOP_LOAD_PAGE,
      onPageUnload: EVENTS.POSTOP_PAGE_EXIT,
    },
    [ScheduleFilterProviderContext.STAFF_MANAGEMENT]: {
      onChangeSites: EVENTS.STAFF_MANAGEMENT_FILTER_SITES,
      onChangeRooms: EVENTS.STAFF_MANAGEMENT_FILTER_ROOMS,
      onChangeDailyMetric: EVENTS.STAFF_MANAGEMENT_FILTER_DAILY_METRIC,
      onChangeDate: EVENTS.STAFF_MANAGEMENT_FILTER_DATE,
      onChangeLayout: EVENTS.STAFF_MANAGEMENT_FILTER_LAYOUT,
      onChangeSurgeons: EVENTS.STAFF_MANAGEMENT_FILTER_SURGEONS,
      onChangeTimeRange: EVENTS.STAFF_MANAGEMENT_FILTER_TIME_RANGE,
      onToggleFilters: EVENTS.STAFF_MANAGEMENT_TOGGLE_FILTERS,
      onToggleScheduled: EVENTS.STAFF_MANAGEMENT_TOGGLE_SCHEDULED,
      onToggleShowClosedRooms: EVENTS.STAFF_MANAGEMENT_TOGGLE_SHOW_CLOSED_ROOMS,
      onToggleShowMetrics: EVENTS.STAFF_MANAGEMENT_TOGGLE_SHOW_METRICS,
      onToggleSortKey: EVENTS.STAFF_MANAGEMENT_TOGGLE_SORT_KEY,
      onPageLoad: EVENTS.STAFF_MANAGEMENT_LOAD_PAGE,
      onPageUnload: EVENTS.STAFF_MANAGEMENT_PAGE_EXIT,
    },
    [ScheduleFilterProviderContext.EDIT_SCHEDULE]: {
      onChangeSites: EVENTS.EDIT_SCHEDULE_FILTER_SITES,
      onChangeRooms: EVENTS.EDIT_SCHEDULE_FILTER_ROOMS,
      onChangeDate: EVENTS.EDIT_SCHEDULE_FILTER_DATE,
      onChangeSurgeons: EVENTS.EDIT_SCHEDULE_FILTER_SURGEONS,
      onChangeDailyMetric: EVENTS.EDIT_SCHEDULE_FILTER_DAILY_METRIC,
      onChangeLayout: EVENTS.EDIT_SCHEDULE_FILTER_LAYOUT,
      onChangeTimeRange: EVENTS.EDIT_SCHEDULE_FILTER_TIME_RANGE,
      onToggleFilters: EVENTS.EDIT_SCHEDULE_TOGGLE_FILTERS,
      onToggleScheduled: EVENTS.EDIT_SCHEDULE_TOGGLE_SCHEDULED,
      onToggleShowClosedRooms: EVENTS.EDIT_SCHEDULE_TOGGLE_SHOW_CLOSED_ROOMS,
      onToggleShowMetrics: EVENTS.EDIT_SCHEDULE_TOGGLE_SHOW_METRICS,
      onToggleSortKey: EVENTS.EDIT_SCHEDULE_TOGGLE_SORT_KEY,
      onPageLoad: EVENTS.EDIT_SCHEDULE_LOAD_PAGE,
      onPageUnload: EVENTS.EDIT_SCHEDULE_EXIT,
    },
    [ScheduleFilterProviderContext.LIVE]: {
      onChangeSites: EVENTS.LIVE_PAGE_FILTER_SITES,
      onChangeRooms: EVENTS.LIVE_PAGE_FILTER_ROOMS,
      onChangeDailyMetric: EVENTS.LIVE_PAGE_FILTER_DAILY_METRIC,
      onChangeDate: EVENTS.LIVE_PAGE_FILTER_DATE,
      onChangeLayout: EVENTS.LIVE_PAGE_FILTER_LAYOUT,
      onChangeSurgeons: EVENTS.LIVE_PAGE_FILTER_SURGEONS,
      onChangeTimeRange: EVENTS.LIVE_PAGE_FILTER_TIME_RANGE,
      onToggleFilters: EVENTS.LIVE_PAGE_TOGGLE_FILTERS,
      onToggleScheduled: EVENTS.LIVE_PAGE_TOGGLE_SCHEDULED,
      onToggleShowClosedRooms: EVENTS.LIVE_PAGE_TOGGLE_SHOW_CLOSED_ROOMS,
      onToggleShowMetrics: EVENTS.LIVE_PAGE_TOGGLE_SHOW_METRICS,
      onToggleSortKey: EVENTS.LIVE_PAGE_TOGGLE_SORT_KEY,
      onPageLoad: EVENTS.LIVE_PAGE_LOAD_PAGE,
      onPageUnload: EVENTS.LIVE_PAGE_EXIT,
    },
    [ScheduleFilterProviderContext.TURNOVERS]: {
      onChangeSites: EVENTS.TURNOVERS_DASHBOARD_FILTER_SITES,
      onChangeRooms: EVENTS.TURNOVERS_DASHBOARD_FILTER_ROOMS,
      onChangeDailyMetric: EVENTS.TURNOVERS_DASHBOARD_FILTER_DAILY_METRIC,
      onChangeDate: EVENTS.TURNOVERS_DASHBOARD_FILTER_DATE,
      onChangeLayout: EVENTS.TURNOVERS_DASHBOARD_FILTER_LAYOUT,
      onChangeSurgeons: EVENTS.TURNOVERS_DASHBOARD_FILTER_SURGEONS,
      onChangeTimeRange: EVENTS.TURNOVERS_DASHBOARD_FILTER_TIME_RANGE,
      onToggleFilters: EVENTS.TURNOVERS_DASHBOARD_TOGGLE_FILTERS,
      onToggleScheduled: EVENTS.TURNOVERS_DASHBOARD_TOGGLE_SCHEDULED,
      onToggleShowClosedRooms:
        EVENTS.TURNOVERS_DASHBOARD_TOGGLE_SHOW_CLOSED_ROOMS,
      onToggleShowMetrics: EVENTS.TURNOVERS_DASHBOARD_TOGGLE_SHOW_METRICS,
      onToggleSortKey: EVENTS.TURNOVERS_DASHBOARD_TOGGLE_SORT_KEY,
      onPageLoad: EVENTS.TURNOVERS_DASHBOARD_LOAD_PAGE,
      onPageUnload: EVENTS.TURNOVERS_DASHBOARD_PAGE_EXIT,
    },
  } as const
