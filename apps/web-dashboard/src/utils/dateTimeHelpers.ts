import { DateTime } from 'luxon'

export const isSameDate = (dateTime1: DateTime, dateTime2: DateTime): boolean =>
  dateTime1.hasSame(dateTime2, 'day') &&
  dateTime1.hasSame(dateTime2, 'month') &&
  dateTime1.hasSame(dateTime2, 'year')

export const isToday = (dateTime: DateTime): boolean =>
  isSameDate(DateTime.now(), dateTime)

export type DateStatus = 'Past' | 'Present' | 'Future'

export const getDateStatus = (dateTime: DateTime, timezone: string): DateStatus => {
  const now = DateTime.now().setZone(timezone)
  const selectedDate = dateTime.setZone(timezone)

  if (selectedDate.startOf('day') < now.startOf('day')) {
    return 'Past'
  } else if (selectedDate.startOf('day') > now.startOf('day')) {
    return 'Future'
  } else {
    return 'Present'
  }
}
